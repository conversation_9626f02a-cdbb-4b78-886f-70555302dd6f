#!/usr/bin/env python3
"""
Enhanced Typing Macro with OCR - Captures text from screen and types it realistically
Requires: pip install pyautogui keyboard pillow pytesseract opencv-python
Also requires: Tesseract OCR installed on system
"""

import pyautogui
import keyboard
import time
import random
import string
import threading
from tkinter import *
from tkinter import ttk, messagebox, scrolledtext
import sys
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageEnhance, ImageFilter
import pytesseract
import os

class OCRTypingMacro:
    def __init__(self):
        self.is_typing = False
        self.stop_typing = False
        self.ocr_text = ""
        self.selection_active = False
        self.start_x = 0
        self.start_y = 0
        self.end_x = 0
        self.end_y = 0
        
        # Setup GUI
        self.root = Tk()
        self.root.title("OCR Typing Macro")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # Variables
        self.speed_var = StringVar(value="fast")
        self.typo_var = StringVar(value="none")
        self.hotkey_var = StringVar(value="f9")
        self.ocr_hotkey_var = StringVar(value="f8")
        
        self.setup_gui()
        self.setup_hotkeys()
        
        # Keyboard layout for realistic typos
        self.keyboard_map = {
            'a': 'sqzw', 'b': 'vghn', 'c': 'xvdf', 'd': 'sfre', 'e': 'wrd3',
            'f': 'dgrt', 'g': 'fhyt', 'h': 'gjyu', 'i': 'uko8', 'j': 'hkiu',
            'k': 'jlio', 'l': 'kop', 'm': 'njk', 'n': 'bmjh', 'o': 'ilp9',
            'p': 'ol0', 'q': 'wa12', 'r': 'etdf', 's': 'awedz', 't': 'rgfy',
            'u': 'yhi7', 'v': 'cfgb', 'w': 'qase', 'x': 'zsdc', 'y': 'tghu',
            'z': 'xsaa'
        }
        
        # Try to find tesseract
        self.setup_tesseract()
        
    def setup_tesseract(self):
        """Setup tesseract path for different systems"""
        possible_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            '/usr/bin/tesseract',
            '/usr/local/bin/tesseract',
            '/opt/homebrew/bin/tesseract'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                return
                
        # If not found, assume it's in PATH
        try:
            pytesseract.get_tesseract_version()
        except:
            messagebox.showerror("Error", 
                "Tesseract OCR not found! Please install it:\n"
                "Windows: https://github.com/UB-Mannheim/tesseract/wiki\n"
                "Mac: brew install tesseract\n"
                "Linux: sudo apt install tesseract-ocr")
    
    def setup_gui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(W, E, N, S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 OCR Typing Macro", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # OCR Section
        ocr_frame = ttk.LabelFrame(main_frame, text="OCR Screen Capture", padding="10")
        ocr_frame.grid(row=1, column=0, columnspan=2, sticky=(W, E), pady=5)
        ocr_frame.columnconfigure(1, weight=1)
        
        self.capture_btn = ttk.Button(ocr_frame, text="📷 Capture Screen Area (F8)", 
                                     command=self.start_screen_capture)
        self.capture_btn.grid(row=0, column=0, sticky=W, pady=2)
        
        self.auto_capture_var = BooleanVar(value=True)
        auto_check = ttk.Checkbutton(ocr_frame, text="Auto-type after capture", 
                                   variable=self.auto_capture_var)
        auto_check.grid(row=0, column=1, sticky=W, padx=(20, 0))
        
        # OCR Preview
        ttk.Label(ocr_frame, text="Detected Text:").grid(row=1, column=0, sticky=W, pady=(10, 0))
        self.ocr_preview = scrolledtext.ScrolledText(ocr_frame, height=4, width=60)
        self.ocr_preview.grid(row=2, column=0, columnspan=2, sticky=(W, E), pady=5)
        
        # Manual text input (fallback)
        ttk.Label(main_frame, text="Manual Text (if OCR fails):").grid(row=2, column=0, sticky=W, pady=(10, 5))
        self.text_area = scrolledtext.ScrolledText(main_frame, height=6, width=50)
        self.text_area.grid(row=3, column=0, columnspan=2, sticky=(W, E, N, S), pady=5)
        self.text_area.insert("1.0", "k4DMgaiqfiLWrFNUbHWyXmQ83zwK&hbeMqTBGsCKF4hbUUNmfQ+8pQaC8C7XZWB2tQWZphgt14EHRa8fO32mriRF33FTV&bv@#zh3RalhzT2wN&eKqr8vVNVHdqsC7f+mnt7OTax+ap3vFqe8VQLTXwEUJ2hCqjK7ur@KTg6vTGwwc&ZM#TMWQ27+Fo42a@D&B&ATA2wAfTO3WA3WB")
        
        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="10")
        settings_frame.grid(row=4, column=0, columnspan=2, sticky=(W, E), pady=10)
        settings_frame.columnconfigure(1, weight=1)
        
        # Speed setting
        ttk.Label(settings_frame, text="Typing Speed:").grid(row=0, column=0, sticky=W, padx=(0, 10))
        speed_combo = ttk.Combobox(settings_frame, textvariable=self.speed_var, 
                                  values=["very_slow", "slow", "medium", "fast", "very_fast"],
                                  state="readonly")
        speed_combo.grid(row=0, column=1, sticky=W, pady=2)
        
        # Typo rate setting
        ttk.Label(settings_frame, text="Typo Rate:").grid(row=1, column=0, sticky=W, padx=(0, 10))
        typo_combo = ttk.Combobox(settings_frame, textvariable=self.typo_var,
                                 values=["none", "very_low", "low", "medium", "high"],
                                 state="readonly")
        typo_combo.grid(row=1, column=1, sticky=W, pady=2)
        
        # Hotkey settings
        ttk.Label(settings_frame, text="Type Hotkey:").grid(row=2, column=0, sticky=W, padx=(0, 10))
        hotkey_combo = ttk.Combobox(settings_frame, textvariable=self.hotkey_var,
                                   values=["f9", "f10", "f11", "f12", "ctrl+space", "alt+space"],
                                   state="readonly")
        hotkey_combo.grid(row=2, column=1, sticky=W, pady=2)
        hotkey_combo.bind('<<ComboboxSelected>>', self.update_hotkeys)
        
        ttk.Label(settings_frame, text="OCR Hotkey:").grid(row=3, column=0, sticky=W, padx=(0, 10))
        ocr_hotkey_combo = ttk.Combobox(settings_frame, textvariable=self.ocr_hotkey_var,
                                       values=["f8", "f7", "f6", "f5", "ctrl+alt+c"],
                                       state="readonly")
        ocr_hotkey_combo.grid(row=3, column=1, sticky=W, pady=2)
        ocr_hotkey_combo.bind('<<ComboboxSelected>>', self.update_hotkeys)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)
        
        self.start_btn = ttk.Button(button_frame, text="🚀 Start Typing (F9)", 
                                   command=self.start_typing)
        self.start_btn.pack(side=LEFT, padx=5)
        
        self.stop_btn = ttk.Button(button_frame, text="⏹ Stop (ESC)", 
                                  command=self.stop_typing_macro, state=DISABLED)
        self.stop_btn.pack(side=LEFT, padx=5)
        
        self.clear_btn = ttk.Button(button_frame, text="🧹 Clear OCR", 
                                   command=self.clear_ocr)
        self.clear_btn.pack(side=LEFT, padx=5)
        
        # Status
        self.status_var = StringVar(value="Ready - Press F8 to capture text from screen")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                foreground="green")
        status_label.grid(row=6, column=0, columnspan=2, pady=10)
        
        # Instructions
        instructions = """📋 Instructions:
1. Position your window with the text you want to capture
2. Press F8 (or click Capture) - you'll have 3 seconds to get ready
3. Move mouse to TOP-LEFT corner of text and press SPACE
4. Move mouse to BOTTOM-RIGHT corner of text and press ENTER
5. Text will be captured and shown in the preview
6. Click in your target input field where you want to type
7. Press F9 to start realistic typing with the captured text

💡 Tips:
• Your window stays focused - no need to switch between windows
• Works great for random character strings, CAPTCHAs, etc.
• Auto-type option will immediately type after OCR capture
• Manual text area is a fallback if OCR doesn't work perfectly
• Press ESC anytime to stop typing or cancel capture"""
        
        inst_label = ttk.Label(main_frame, text=instructions, justify=LEFT, 
                              font=("Arial", 9), foreground="gray")
        inst_label.grid(row=7, column=0, columnspan=2, pady=(10, 0), sticky=W)
    
    def setup_hotkeys(self):
        try:
            keyboard.clear_all_hotkeys()
        except AttributeError:
            # Handle compatibility issue with keyboard library
            try:
                keyboard.unhook_all_hotkeys()
            except:
                pass
        keyboard.add_hotkey(self.hotkey_var.get(), self.start_typing)
        keyboard.add_hotkey(self.ocr_hotkey_var.get(), self.start_screen_capture)
        keyboard.add_hotkey('esc', self.stop_typing_macro)
        
    def update_hotkeys(self, event=None):
        self.setup_hotkeys()
        self.start_btn.config(text=f"🚀 Start Typing ({self.hotkey_var.get().upper()})")
        self.capture_btn.config(text=f"📷 Capture Screen Area ({self.ocr_hotkey_var.get().upper()})")
    
    def start_screen_capture(self):
        """Start screen capture process"""
        if self.is_typing:
            return

        self.status_var.set("Get ready to select area in 3 seconds...")
        self.root.update()

        # Give user time to position their window and get ready
        for i in range(3, 0, -1):
            self.status_var.set(f"Get ready to select area in {i} seconds...")
            self.root.update()
            time.sleep(1)

        self.status_var.set("Move mouse to top-left corner of text, then press SPACE")

        # Hide main window completely
        self.root.withdraw()
        self.root.update()
        time.sleep(0.1)

        # Start coordinate selection mode
        self.start_coordinate_selection()
    
    def start_coordinate_selection(self):
        """Start a non-intrusive coordinate selection process"""
        # Initialize coordinates
        self.start_x = None
        self.start_y = None
        self.end_x = None
        self.end_y = None

        # Set up keyboard listeners for coordinate capture
        keyboard.add_hotkey('space', self.capture_start_position)
        keyboard.add_hotkey('enter', self.capture_end_position)
        keyboard.add_hotkey('esc', self.cancel_coordinate_selection)

        # Take a screenshot for later use
        self.full_screenshot = pyautogui.screenshot()

        # Show a small notification window
        self.show_notification_window("Position mouse at TOP-LEFT corner of text and press SPACE")

    def show_notification_window(self, message):
        """Show a small notification window"""
        self.notification_window = Toplevel()
        self.notification_window.overrideredirect(True)
        self.notification_window.attributes('-topmost', True)
        self.notification_window.geometry(f"400x50+{10}+{10}")
        self.notification_window.configure(bg='black')

        label = Label(self.notification_window, text=message,
                     fg='yellow', bg='black', font=('Arial', 12, 'bold'))
        label.pack(fill=BOTH, expand=True)

        # Auto-close after 10 seconds
        self.notification_window.after(10000, self.notification_window.destroy)

    def capture_start_position(self):
        """Capture the starting position (top-left corner)"""
        self.start_x, self.start_y = pyautogui.position()

        # Update notification
        if hasattr(self, 'notification_window'):
            self.notification_window.destroy()

        self.show_notification_window("Now position mouse at BOTTOM-RIGHT corner and press ENTER")

    def capture_end_position(self):
        """Capture the ending position (bottom-right corner)"""
        self.end_x, self.end_y = pyautogui.position()

        # Clean up notification
        if hasattr(self, 'notification_window'):
            self.notification_window.destroy()

        # Clean up keyboard hooks
        try:
            keyboard.remove_hotkey('space')
            keyboard.remove_hotkey('enter')
            keyboard.remove_hotkey('esc')
        except:
            pass

        # Process the selection
        self.process_coordinate_selection()

    def cancel_coordinate_selection(self):
        """Cancel the coordinate selection process"""
        # Clean up notification
        if hasattr(self, 'notification_window'):
            self.notification_window.destroy()

        # Clean up keyboard hooks
        keyboard.unhook_key('space')
        keyboard.unhook_key('enter')
        keyboard.unhook_key('esc')

        # Restore main window
        self.restore_main_window()
        self.status_var.set("Capture cancelled")

    def process_coordinate_selection(self):
        """Process the captured coordinates and perform OCR"""
        if self.start_x is None or self.start_y is None or self.end_x is None or self.end_y is None:
            self.restore_main_window()
            self.status_var.set("Invalid selection - please try again")
            return

        # Ensure coordinates are in correct order
        x1, x2 = sorted([self.start_x, self.end_x])
        y1, y2 = sorted([self.start_y, self.end_y])

        # Validate selection size
        if abs(x2 - x1) < 10 or abs(y2 - y1) < 10:
            self.restore_main_window()
            self.status_var.set("Selection too small - please try again")
            return

        # Restore main window first
        self.restore_main_window()

        # Capture the selected area from the screenshot
        self.capture_and_ocr(x1, y1, x2, y2)
    
    def start_selection(self, event):
        self.start_x = event.x
        self.start_y = event.y
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
    
    def update_selection(self, event):
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
        self.selection_rect = self.canvas.create_rectangle(
            self.start_x, self.start_y, event.x, event.y,
            outline='red', width=3, fill='', stipple='gray25'
        )
    
    def end_selection(self, event):
        self.end_x = event.x
        self.end_y = event.y

        # Ensure coordinates are in correct order
        x1, x2 = sorted([self.start_x, self.end_x])
        y1, y2 = sorted([self.start_y, self.end_y])

        # Validate selection size (minimum 10x10 pixels)
        if abs(x2 - x1) < 10 or abs(y2 - y1) < 10:
            self.cancel_selection(event)
            self.status_var.set("Selection too small - please select a larger area")
            return

        # Close selection window and restore main window
        self.selection_window.destroy()
        self.restore_main_window()

        # Capture the selected area
        self.capture_and_ocr(x1, y1, x2, y2)
    
    def cancel_selection(self, event):
        self.selection_window.destroy()
        self.restore_main_window()
        self.status_var.set("Capture cancelled")

    def restore_main_window(self):
        """Properly restore the main window after capture"""
        self.root.deiconify()  # Show main window
        self.root.lift()       # Bring to front
        self.root.focus_force()  # Force focus
        self.root.update()     # Update display
    
    def capture_and_ocr(self, x1, y1, x2, y2):
        """Capture selected area and perform OCR"""
        try:
            self.status_var.set("Performing OCR...")

            # Use the stored screenshot if available, otherwise take a new one
            if hasattr(self, 'full_screenshot') and self.full_screenshot:
                # Crop from the stored screenshot
                screenshot = self.full_screenshot.crop((x1, y1, x2, y2))
            else:
                # Fallback to taking a new screenshot
                screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            
            # Convert to numpy array for OpenCV processing
            img_np = np.array(screenshot)
            img_cv = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
            
            # Preprocess image for better OCR
            img_processed = self.preprocess_image(img_cv)
            
            # Perform OCR with different configurations
            ocr_text = self.perform_ocr(img_processed)
            
            if ocr_text.strip():
                self.ocr_text = ocr_text.strip()
                self.ocr_preview.delete("1.0", END)
                self.ocr_preview.insert("1.0", self.ocr_text)
                self.status_var.set(f"OCR completed! Captured {len(self.ocr_text)} characters")
                
                # Auto-type if enabled
                if self.auto_capture_var.get():
                    self.root.after(1000, self.start_typing)  # Wait 1 second then type
            else:
                self.status_var.set("OCR failed - no text detected")
                messagebox.showwarning("OCR Failed", "No text could be detected in the selected area.")
                
        except Exception as e:
            self.status_var.set(f"OCR Error: {str(e)}")
            messagebox.showerror("OCR Error", f"Error performing OCR: {str(e)}")
    
    def preprocess_image(self, img):
        """Preprocess image for better OCR accuracy"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Resize image (OCR works better on larger images)
        height, width = gray.shape
        if height < 50:
            scale_factor = 3
            gray = cv2.resize(gray, (width * scale_factor, height * scale_factor), 
                            interpolation=cv2.INTER_CUBIC)
        
        # Apply different preprocessing techniques
        # Method 1: Simple threshold
        _, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Method 2: Adaptive threshold
        thresh2 = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY, 11, 2)
        
        # Method 3: Morphological operations
        kernel = np.ones((1, 1), np.uint8)
        thresh3 = cv2.morphologyEx(thresh1, cv2.MORPH_CLOSE, kernel)
        
        return [gray, thresh1, thresh2, thresh3]
    
    def perform_ocr(self, processed_images):
        """Try OCR with different image preprocessing and configurations"""
        # Different OCR configurations
        configs = [
            '--psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+@#&',
            '--psm 7 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+@#&',
            '--psm 13',
            '--psm 8',
            '--psm 7',
            '--psm 6'
        ]
        
        best_result = ""
        
        for img in processed_images:
            for config in configs:
                try:
                    result = pytesseract.image_to_string(img, config=config).strip()
                    if len(result) > len(best_result):
                        best_result = result
                except:
                    continue
        
        return best_result
    
    def clear_ocr(self):
        """Clear OCR results"""
        self.ocr_text = ""
        self.ocr_preview.delete("1.0", END)
        self.status_var.set("OCR cleared")
    
    def get_typing_speed(self):
        speeds = {
            "very_slow": (200, 400),
            "slow": (150, 300),
            "medium": (100, 200),
            "fast": (50, 120),        # Faster for long random strings
            "very_fast": (30, 80)     # Very fast for efficiency
        }
        return speeds.get(self.speed_var.get(), speeds["fast"])
    
    def get_typo_rate(self):
        rates = {
            "none": 0.0,
            "very_low": 0.005,        # Very low for random strings
            "low": 0.01,
            "medium": 0.03,
            "high": 0.05
        }
        return rates.get(self.typo_var.get(), rates["none"])
    
    def should_make_typo(self):
        return random.random() < self.get_typo_rate()
    
    def get_typo_char(self, char):
        char_lower = char.lower()
        if char_lower in self.keyboard_map:
            nearby_chars = self.keyboard_map[char_lower]
            typo_char = random.choice(nearby_chars)
            return typo_char.upper() if char.isupper() else typo_char
        return random.choice(string.ascii_lowercase)
    
    def get_random_delay(self):
        min_delay, max_delay = self.get_typing_speed()
        delay = random.uniform(min_delay, max_delay) / 1000.0
        
        # Less variation for random character strings (more consistent)
        if random.random() < 0.05:  # Only 5% chance of longer pause
            delay *= random.uniform(1.5, 2.5)
            
        return delay
    
    def type_text_realistically(self, text):
        self.is_typing = True
        self.stop_typing = False
        
        try:
            # Small delay to allow user to position cursor
            time.sleep(1)
            
            i = 0
            while i < len(text) and not self.stop_typing:
                char = text[i]
                
                # Check for typo (very rare for random strings)
                if char.isalnum() and self.should_make_typo():
                    # Make typo
                    typo_char = self.get_typo_char(char)
                    pyautogui.write(typo_char)
                    
                    # Pause before correction
                    time.sleep(random.uniform(0.2, 0.5))
                    if self.stop_typing:
                        break
                        
                    # Backspace to correct
                    pyautogui.press('backspace')
                    time.sleep(random.uniform(0.1, 0.2))
                    if self.stop_typing:
                        break
                
                # Type the correct character
                pyautogui.write(char)
                
                # Update status
                progress = int((i + 1) / len(text) * 100)
                self.status_var.set(f"Typing... {progress}% complete ({i+1}/{len(text)})")
                
                # Random delay before next character
                if not self.stop_typing:
                    time.sleep(self.get_random_delay())
                
                i += 1
            
            if not self.stop_typing:
                self.status_var.set("Typing completed!")
            else:
                self.status_var.set("Typing stopped by user")
                
        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
        finally:
            self.is_typing = False
            self.start_btn.config(state=NORMAL)
            self.stop_btn.config(state=DISABLED)
    
    def start_typing(self):
        if self.is_typing:
            return
        
        # Use OCR text if available, otherwise use manual text
        text_to_type = ""
        if self.ocr_text.strip():
            text_to_type = self.ocr_text
        else:
            text_to_type = self.text_area.get("1.0", END).strip()
            
        if not text_to_type:
            messagebox.showwarning("Warning", "No text to type! Capture some text with OCR first.")
            return
        
        self.start_btn.config(state=DISABLED)
        self.stop_btn.config(state=NORMAL)
        self.status_var.set("Starting in 1 second... Position your cursor!")
        
        # Start typing in a separate thread
        typing_thread = threading.Thread(target=self.type_text_realistically, args=(text_to_type,))
        typing_thread.daemon = True
        typing_thread.start()
    
    def stop_typing_macro(self):
        self.stop_typing = True
        self.start_btn.config(state=NORMAL)
        self.stop_btn.config(state=DISABLED)
        if hasattr(self, 'selection_window'):
            try:
                self.selection_window.destroy()
            except:
                pass
        self.restore_main_window()
        self.status_var.set("Ready - Press F8 to capture text from screen")
    
    def run(self):
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        try:
            keyboard.clear_all_hotkeys()
        except AttributeError:
            # Handle compatibility issue with keyboard library
            try:
                keyboard.unhook_all_hotkeys()
            except:
                pass
        self.root.destroy()
        sys.exit()

if __name__ == "__main__":
    # Check if required modules are installed
    required_modules = {
        'pyautogui': 'pyautogui',
        'keyboard': 'keyboard', 
        'PIL': 'pillow',
        'cv2': 'opencv-python',
        'pytesseract': 'pytesseract'
    }
    
    missing_modules = []
    for module, pip_name in required_modules.items():
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(pip_name)
    
    if missing_modules:
        print("Error: Required modules not installed!")
        print("Please install them with:")
        print(f"pip install {' '.join(missing_modules)}")
        print("\nAlso install Tesseract OCR:")
        print("Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("Mac: brew install tesseract")
        print("Linux: sudo apt install tesseract-ocr")
        sys.exit(1)
    
    # Configure pyautogui
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.01
    
    print("Starting OCR Typing Macro...")
    print("F8 - Capture screen area")
    print("F9 - Start typing")
    print("ESC - Stop/Cancel")
    
    app = OCRTypingMacro()
    app.run()